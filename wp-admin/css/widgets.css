/* General Widgets Styles */

.widget {
	margin: 0 auto 10px;
	position: relative;
	box-sizing: border-box;
}

.widget.open {
	z-index: 99;
}
.widget.open:focus-within {
	z-index: 100;
}

.widget-top {
	font-size: 13px;
	font-weight: 600;
	background: #f6f7f7;
}

.widget-top .widget-action {
	border: 0;
	margin: 0;
	padding: 10px;
	background: none;
	cursor: pointer;
}

.widget-title h3,
.widget-title h4 {
	margin: 0;
	padding: 15px;
	font-size: 1em;
	line-height: 1;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	-webkit-user-select: none;
	user-select: none;
}

.widgets-holder-wrap .widget-inside {
	border-top: none;
	padding: 1px 15px 15px;
	line-height: 1.23076923;
}

.widget.widget-dirty .widget-control-close-wrapper {
	display: none;
}

.in-widget-title,
#widgets-right a.widget-control-edit,
#available-widgets .widget-description {
	color: #646970;
}

.deleting .widget-title,
.deleting .widget-top .widget-action .toggle-indicator:before {
	color: #a7aaad;
}

/* Media Widgets */
.wp-core-ui .media-widget-control.selected .placeholder,
.wp-core-ui .media-widget-control.selected .not-selected,
.wp-core-ui .media-widget-control .selected {
	display: none;
}

.media-widget-control.selected .selected {
	display: inline-block;
}

.media-widget-buttons {
	text-align: left;
	margin-top: 0;
}

.media-widget-control .media-widget-buttons .button {
	width: auto;
	height: auto;
	margin-top: 12px;
	white-space: normal;
}

.media-widget-buttons .button:first-child {
	margin-right: 8px;
}

.media-widget-control .attachment-media-view .button-add-media,
.media-widget-control .placeholder {
	border: 1px dashed #c3c4c7;
	box-sizing: border-box;
	cursor: pointer;
	line-height: 1.6;
	padding: 9px 0;
	position: relative;
	text-align: center;
	width: 100%;
}

.media-widget-control .attachment-media-view .button-add-media {
	cursor: pointer;
	background-color: #f0f0f1;
	color: #2c3338;
}

.media-widget-control .attachment-media-view .button-add-media:hover {
	background-color: #fff;
}

.media-widget-control .attachment-media-view .button-add-media:focus {
	background-color: #fff;
	border-style: solid;
	border-color: #4f94d4;
	box-shadow: 0 0 3px rgba(34, 113, 177, 0.8);
	/* Only visible in Windows High Contrast mode */
	outline: 2px solid transparent;
	outline-offset: -2px;
}

.media-widget-control .media-widget-preview {
	background: transparent;
	text-align: center;
}
.media-widget-control .media-widget-preview .notice {
	text-align: initial;
}
.media-frame .media-widget-embed-notice p code,
.media-widget-control .notice p code {
	padding: 0 3px 0 0;
}
.media-frame .media-widget-embed-notice {
	margin-top: 16px;
}
.media-widget-control .media-widget-preview img {
	max-width: 100%;
	vertical-align: middle;
	background-image: linear-gradient(45deg, #c3c4c7 25%, transparent 25%, transparent 75%, #c3c4c7 75%, #c3c4c7), linear-gradient(45deg, #c3c4c7 25%, transparent 25%, transparent 75%, #c3c4c7 75%, #c3c4c7);
	background-position: 0 0, 10px 10px;
	background-size: 20px 20px;
}
.media-widget-control .media-widget-preview .wp-video-shortcode {
	background: #000;
}

.media-frame.media-widget .media-toolbar-secondary {
	min-width: 300px;
}

.media-frame.media-widget .image-details .embed-media-settings .setting.align,
.media-frame.media-widget .attachment-display-settings .setting.align,
.media-frame.media-widget .embed-media-settings .setting.align,
.media-frame.media-widget .embed-media-settings .legend-inline,
.media-frame.media-widget .embed-link-settings .setting.link-text,
.media-frame.media-widget .replace-attachment,
.media-frame.media-widget .checkbox-setting.autoplay {
	display: none;
}

.media-widget-video-preview {
	width: 100%;
}

.media-widget-video-link {
	display: inline-block;
	min-height: 132px;
	width: 100%;
	background: #000;
}

.media-widget-video-link .dashicons {
	font: normal 60px/1 'dashicons';
	position: relative;
	width: 100%;
	top: -90px;
	color: #fff;
	text-decoration: none;
}

.media-widget-video-link.no-poster .dashicons {
	top: 30px;
}

.media-frame #embed-url-field.invalid,
.media-widget-image-link > .link:invalid {
	border: 1px solid #d63638;
}

.media-widget-image-link {
	margin: 1em 0;
}

.media-widget-gallery-preview {
	display: flex;
	justify-content: flex-start;
	flex-wrap: wrap;
	margin: -1.79104477%;
}

.media-widget-preview.media_gallery,
.media-widget-preview.media_image {
	cursor: pointer;
}

.media-widget-preview .placeholder {
	background: #f0f0f1;
}

.media-widget-gallery-preview .gallery-item {
	box-sizing: border-box;
	width: 50%;
	margin: 0;
	background: transparent;
}

.media-widget-gallery-preview .gallery-item .gallery-icon {
	margin: 4.5%;
}

/*
 * Use targeted nth-last-child selectors to control the size of each image
 * based on how many gallery items are present in the grid.
 * See: https://alistapart.com/article/quantity-queries-for-css
 */
.media-widget-gallery-preview .gallery-item:nth-last-child(3):first-child,
.media-widget-gallery-preview .gallery-item:nth-last-child(3):first-child ~ .gallery-item,
.media-widget-gallery-preview .gallery-item:nth-last-child(n+5),
.media-widget-gallery-preview .gallery-item:nth-last-child(n+5) ~ .gallery-item,
.media-widget-gallery-preview .gallery-item:nth-last-child(n+6),
.media-widget-gallery-preview .gallery-item:nth-last-child(n+6) ~ .gallery-item {
	max-width: 33.33%;
}

.media-widget-gallery-preview .gallery-item img {
	height: auto;
	vertical-align: bottom;
}

.media-widget-gallery-preview .gallery-icon {
	position: relative;
}

.media-widget-gallery-preview .gallery-icon-placeholder {
	position: absolute;
	top: 0;
	bottom: 0;
	width: 100%;
	box-sizing: border-box;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: rgba(0, 0, 0, 0.5);
}

.media-widget-gallery-preview .gallery-icon-placeholder-text {
	font-weight: 600;
	font-size: 2em;
	color: #fff;
}


/* Widget Dragging Helpers */
.widget.ui-draggable-dragging {
	min-width: 100%;
}

.widget.ui-sortable-helper {
	opacity: 0.8;
}

.widget-placeholder {
	border: 1px dashed #c3c4c7;
	margin: 0 auto 10px;
	height: 45px;
	width: 100%;
	box-sizing: border-box;
}

#widgets-right .widget-placeholder {
	margin-top: 0;
}

#widgets-right .closed .widget-placeholder {
	height: 0;
	border: 0;
	margin-top: -10px;
}

/* Widget Sidebars */
.sidebar-name {
	position: relative;
	box-sizing: border-box;
}

.js .sidebar-name {
	cursor: pointer;
}

.sidebar-name .handlediv {
	float: right;
	width: 38px;
	height: 38px;
	border: 0;
	margin: 0;
	padding: 8px;
	background: none;
	cursor: pointer;
	outline: none;
}

#widgets-right .sidebar-name .handlediv {
	margin: 5px 3px 0 0;
}

.sidebar-name .handlediv:focus {
	box-shadow: none;
	/* Only visible in Windows High Contrast mode */
	outline: 1px solid transparent;
}

#widgets-left .sidebar-name .toggle-indicator {
	display: none;
}

#widgets-left .widgets-holder-wrap.closed .sidebar-name .toggle-indicator,
#widgets-left .sidebar-name:hover .toggle-indicator,
#widgets-left .sidebar-name .handlediv:focus .toggle-indicator {
	display: block;
}

.sidebar-name .toggle-indicator:before {
	padding: 1px 2px 1px 0;
	border-radius: 50%;
}

.sidebar-name .handlediv:focus .toggle-indicator:before {
	box-shadow: 0 0 0 2px #2271b1;
	/* Only visible in Windows High Contrast mode */
	outline: 2px solid transparent;
}

.sidebar-name h2,
.sidebar-name h3 {
	margin: 0;
	padding: 8px 10px;
	overflow: hidden;
	white-space: normal;
	line-height: 1.5;
}

.widgets-holder-wrap .description {
	padding: 0 0 15px;
	margin: 0;
	font-style: normal;
	color: #646970;
}

.widget-holder .description,
.inactive-sidebar .description {
	color: #50575e;
}

#widgets-right .widgets-holder-wrap .description {
	padding-left: 7px;
	padding-right: 7px;
}

/* Widgets 2-col Layout */
div.widget-liquid-left {
	margin: 0;
	width: 38%;
	float: left;
}

div.widget-liquid-right {
	float: right;
	width: 58%;
}

/* Widgets Left - Available Widgets */

div#widgets-left {
	padding-top: 12px;
}

div#widgets-left .closed .sidebar-name,
div#widgets-left .inactive-sidebar.closed .sidebar-name {
	margin-bottom: 10px;
}

div#widgets-left .sidebar-name h2,
div#widgets-left .sidebar-name h3 {
	padding: 10px 0;
	margin: 0 10px 0 0;
}

#widgets-left .widgets-holder-wrap,
div#widgets-left .widget-holder {
	background: transparent;
	border: none;
}

#widgets-left .widgets-holder-wrap {
	border: none;
	box-shadow: none;
}

#available-widgets .widget {
	margin: 0;
}

#available-widgets .widget:nth-child(odd) {
	clear: both;
}

#available-widgets .widget .widget-description {
	display: block;
	padding: 10px 15px;
	font-size: 12px;
	overflow-wrap: break-word;
	word-wrap: break-word;
	-ms-word-break: break-all;
	word-break: break-word;
	-webkit-hyphens: auto;
	hyphens: auto;
}

#available-widgets #widget-list {
	position: relative;
}

/* Inactive Sidebars */
#widgets-left .inactive-sidebar {
	clear: both;
	width: 100%;
	background: transparent;
	padding: 0;
	margin: 0 0 20px;
	border: none;
	box-shadow: none;
}

#widgets-left .inactive-sidebar.first {
	margin-top: 40px;
}

/* Not sure what this is for... */
div#widgets-left .inactive-sidebar .widget.expanded {
	left: auto;
}

.widget-title-action {
	float: right;
	position: relative;
}

div#widgets-left .inactive-sidebar .widgets-sortables {
	min-height: 42px;
	padding: 0;
	background: transparent;
	margin: 0;
	position: relative;
}

/* Widgets Right */

div#widgets-right .sidebars-column-1,
div#widgets-right .sidebars-column-2 {
	max-width: 450px;
}

div#widgets-right .widgets-holder-wrap {
	margin: 10px 0 0;
}

div#widgets-right .sidebar-description {
	min-height: 20px;
	margin-top: -5px;
}

div#widgets-right .sidebar-name h2,
div#widgets-right .sidebar-name h3 {
	padding: 15px 15px 15px 7px;
}

div#widgets-right .widget-top {
	padding: 0;
}

div#widgets-right .widgets-sortables {
	padding: 0 8px;
	margin-bottom: 9px;
	position: relative;
	min-height: 123px;
}

div#widgets-right .closed .widgets-sortables {
	min-height: 0;
	margin-bottom: 0;
}

.sidebar-name .spinner,
.remove-inactive-widgets .spinner {
	float: none;
	position: relative;
	top: -2px;
	margin: -5px 5px;
}

.sidebar-name .spinner {
	position: absolute;
	top: 18px;
	right: 30px;
}

/* Dragging a widget over a closed sidebar */
#widgets-right .widgets-holder-wrap.widget-hover {
	border-color: #787c82;
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Accessibility Mode */
.widget-access-link {
	float: right;
	margin: -5px 0 10px 10px;
}

.widgets_access #widgets-left .widget .widget-top {
	cursor: auto;
}

.widgets_access #wpwrap .widgets-holder-wrap.closed .sidebar-description,
.widgets_access #wpwrap .widgets-holder-wrap.closed .widget,
.widgets_access #wpwrap .widget-control-edit {
	display: block;
}

.widgets_access #widgets-left .widget .widget-top:hover,
.widgets_access #widgets-right .widget .widget-top:hover {
	border-color: #dcdcde;
}

#available-widgets .widget-control-edit .edit,
#available-widgets .widget-action .edit,
#widgets-left .inactive-sidebar .widget-control-edit .add,
#widgets-left .inactive-sidebar .widget-action .add,
#widgets-right .widget-control-edit .add,
#widgets-right .widget-action .add {
	display: none;
}

.widget-control-edit {
	display: block;
	color: #646970;
	background: #f0f0f1;
	padding: 0 15px;
	line-height: 3.30769230;
	border-left: 1px solid #dcdcde;
}

#widgets-left .widget-control-edit:hover,
#widgets-right .widget-control-edit:hover {
	color: #fff;
	background: #3c434a;
	border-left: 0;
	outline: 1px solid #3c434a;
}

.widgets-holder-wrap .sidebar-name,
.widgets-holder-wrap .sidebar-description {
	-webkit-user-select: none;
	user-select: none;
}

.editwidget {
	margin: 0 auto;
}

.editwidget .widget-inside {
	display: block;
	padding: 0 15px;
}

.editwidget .widget-control-actions {
	margin-top: 20px;
}

.js .widgets-holder-wrap.closed .widget,
.js .widgets-holder-wrap.closed .sidebar-description,
.js .widgets-holder-wrap.closed .remove-inactive-widgets,
.js .widgets-holder-wrap.closed .description,
.js .closed br.clear {
	display: none;
}

.js .widgets-holder-wrap.closed .widget.ui-sortable-helper {
	display: block;
}

/* Hide Widget Settings by Default */
.widget-inside,
.widget-description {
	display: none;
}

.widget-inside {
	background: #fff;
}

.widget-inside select {
	max-width: 100%;
}

/* Dragging widgets over the available widget area show's a "Deactivate" message */
#removing-widget {
	display: none;
	font-weight: 400;
	padding-left: 15px;
	font-size: 12px;
	line-height: 1;
	color: #000;
}

.js #removing-widget {
	color: #72aee6;
}

.widget-control-noform,
#access-off,
.widgets_access .widget-action,
.widgets_access .handlediv,
.widgets_access #access-on,
.widgets_access .widget-holder .description,
.no-js .widget-holder .description {
	display: none;
}

.widgets_access .widget-holder,
.widgets_access #widget-list {
	padding-top: 10px;
}

.widgets_access #access-off {
	display: inline;
}

.widgets_access .sidebar-name,
.widgets_access .widget .widget-top {
	cursor: default;
}


/* Widgets Area Chooser */
.widget-liquid-left #widgets-left.chooser #available-widgets .widget,
.widget-liquid-left #widgets-left.chooser .inactive-sidebar {
	transition: opacity 0.1s linear;
}

.widget-liquid-left #widgets-left.chooser #available-widgets .widget,
.widget-liquid-left #widgets-left.chooser .inactive-sidebar {
	/* -webkit-filter: blur(1px); */
	opacity: 0.2;
	pointer-events: none;
}

.widget-liquid-left #widgets-left.chooser #available-widgets .widget-in-question {
	/* -webkit-filter: none; */
	opacity: 1;
	pointer-events: auto;
}

.widgets-chooser ul,
#widgets-left .widget-in-question .widget-top,
#available-widgets .widget-top:hover,
div#widgets-right .widget-top:hover,
#widgets-left .widget-top:hover {
	border-color: #8c8f94;
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.widgets-chooser ul.widgets-chooser-sidebars {
	margin: 0;
	list-style-type: none;
	max-height: 300px;
	overflow: auto;
}

.widgets-chooser {
	display: none;
}

.widgets-chooser ul {
	border: 1px solid #c3c4c7;
}

.widgets-chooser li {
	border-bottom: 1px solid #c3c4c7;
	background: #fff;
	margin: 0;
	position: relative;
}

.widgets-chooser .widgets-chooser-button {
	width: 100%;
	padding: 10px 15px 10px 35px;
	background: transparent;
	border: 0;
	box-sizing: border-box;
	text-align: left;
	cursor: pointer;
	transition: background 0.2s ease-in-out;
}

/* @todo looks like these hover/focus states are overridden by .widgets-chooser-selected */
.widgets-chooser .widgets-chooser-button:hover,
.widgets-chooser .widgets-chooser-button:focus {
	outline: none;
	text-decoration: underline;
}

.widgets-chooser li:last-child {
	border: none;
}

.widgets-chooser .widgets-chooser-selected .widgets-chooser-button {
	background: #2271b1;
	color: #fff;
}

.widgets-chooser .widgets-chooser-selected:before {
	content: "\f147";
	display: block;
	-webkit-font-smoothing: antialiased;
	font: normal 26px/1 dashicons;
	color: #fff;
	position: absolute;
	top: 7px;
	left: 5px;
}

.widgets-chooser .widgets-chooser-actions {
	padding: 10px 0 12px;
	text-align: center;
}

#available-widgets .widget .widget-top {
	cursor: pointer;
}

#available-widgets .widget.ui-draggable-dragging .widget-top {
	cursor: move;
}

/* =Specific widget styling
-------------------------------------------------------------- */
.text-widget-fields {
	position: relative;
}
.text-widget-fields [hidden] {
	display: none;
}
.text-widget-fields .wp-pointer.wp-pointer-top {
	position: absolute;
	z-index: 3;
	top: 100px;
	right: 10px;
	left: 10px;
}
.text-widget-fields .wp-pointer .wp-pointer-arrow {
	left: auto;
	right: 15px;
}
.text-widget-fields .wp-pointer .wp-pointer-buttons {
	line-height: 1.4;
}

.custom-html-widget-fields > p > .CodeMirror {
	border: 1px solid #dcdcde;
}
.custom-html-widget-fields code {
	padding-top: 1px;
	padding-bottom: 1px;
}
ul.CodeMirror-hints {
	z-index: 101; /* Due to z-index 100 set on .widget.open */
}
.widget-control-actions .custom-html-widget-save-button.button.validation-blocked {
	cursor: not-allowed;
}

/* =Media Queries
-------------------------------------------------------------- */

@media screen and (max-width: 782px) {
	.widgets-holder-wrap .widget-inside input[type="checkbox"],
	.widgets-holder-wrap .widget-inside input[type="radio"],
	.editwidget .widget-inside input[type="checkbox"], /* Selectors for the "accessibility mode" page. */
	.editwidget .widget-inside input[type="radio"] {
		margin: 0.25rem 0.25rem 0.25rem 0;
	}
}

@media screen and (max-width: 480px) {
	div.widget-liquid-left {
		width: 100%;
		float: none;
		border-right: none;
		padding-right: 0;
	}

	#widgets-left .sidebar-name {
		margin-right: 0;
	}

	#widgets-left #available-widgets .widget-top {
		margin-right: 0;
	}

	#widgets-left .inactive-sidebar .widgets-sortables {
		margin-right: 0;
	}

	div.widget-liquid-right {
		width: 100%;
		float: none;
	}

	div.widget {
		max-width: 480px;
	}

	.widget-access-link {
		float: none;
		margin: 15px 0 0;
	}
}

@media screen and (max-width: 320px) {
	div.widget {
		max-width: 320px;
	}
}

@media only screen and (min-width: 1250px) {
	#widgets-left #available-widgets .widget {
		width: 49%;
		float: left;
	}

	.widget.ui-draggable-dragging {
		min-width: 49%;
	}

	#widgets-left #available-widgets .widget:nth-child(even) {
		float: right;
	}

	#widgets-right .sidebars-column-1,
	#widgets-right .sidebars-column-2 {
		float: left;
		width: 49%;
	}

	#widgets-right .sidebars-column-1 {
		margin-right: 2%;
	}

	#widgets-right.single-sidebar .sidebars-column-1,
	#widgets-right.single-sidebar .sidebars-column-2 {
		float: none;
		width: 100%;
		margin: 0;
	}
}
